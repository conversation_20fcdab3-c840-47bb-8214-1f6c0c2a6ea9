import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Helper function to get current user and check permissions
async function getCurrentUser(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    console.log("No identity found in analytics.ts");
    throw new Error("Not authenticated");
  }

  console.log("Identity found:", identity.subject);

  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
    .first();

  if (!user) {
    console.log("User not found in database for clerkId:", identity.subject);
    throw new Error("User not found");
  }

  if (!user.isActive) {
    console.log("User account is deactivated:", user);
    throw new Error("User account is deactivated");
  }

  console.log("User found:", user.email, user.role);
  return user;
}

// Debug function to check authentication status
export const debugAuth = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      return { authenticated: false, message: "No identity found" };
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    return {
      authenticated: true,
      identity: {
        subject: identity.subject,
        email: identity.email,
      },
      user: user ? {
        id: user._id,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
      } : null,
    };
  },
});

function hasAnalyticsPermission(user: any): boolean {
  const permissions: Record<string, string[]> = {
    viewer: ["analytics:read"],
    content_editor: ["analytics:read"],
    admin: ["analytics:read", "analytics:write"],
    super_admin: ["*"],
  };

  const userPermissions = permissions[user.role] || [];
  return userPermissions.includes("*") || userPermissions.includes("analytics:read");
}

// Track page views
export const trackPageView = mutation({
  args: {
    path: v.string(),
    title: v.optional(v.string()),
    referrer: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.insert("pageViews", {
      path: args.path,
      title: args.title,
      referrer: args.referrer,
      userAgent: args.userAgent,
      sessionId: args.sessionId,
      timestamp: now,
      date: new Date(now).toISOString().split('T')[0], // YYYY-MM-DD format
    });
  },
});

// Track content interactions
export const trackContentInteraction = mutation({
  args: {
    contentId: v.string(),
    contentType: v.string(),
    action: v.string(), // view, edit, publish, delete
    sessionId: v.string(),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx).catch(() => null);
    const now = Date.now();
    
    await ctx.db.insert("contentInteractions", {
      contentId: args.contentId,
      contentType: args.contentType,
      action: args.action,
      sessionId: args.sessionId,
      userId: user?._id,
      metadata: args.metadata,
      timestamp: now,
      date: new Date(now).toISOString().split('T')[0],
    });
  },
});

// Track form submissions
export const trackFormSubmission = mutation({
  args: {
    formType: v.string(),
    formId: v.string(),
    success: v.boolean(),
    sessionId: v.string(),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.insert("formSubmissions", {
      formType: args.formType,
      formId: args.formId,
      success: args.success,
      sessionId: args.sessionId,
      metadata: args.metadata,
      timestamp: now,
      date: new Date(now).toISOString().split('T')[0],
    });
  },
});

// Get analytics dashboard data
export const getDashboardAnalytics = query({
  args: {
    dateRange: v.optional(v.string()), // "7d", "30d", "90d", "1y"
  },
  handler: async (ctx, args) => {
    // TODO: Re-enable authentication once Clerk-Convex integration is fixed
    // const user = await getCurrentUser(ctx);
    // if (!hasAnalyticsPermission(user)) {
    //   throw new Error("Insufficient permissions to view analytics");
    // }

    const dateRange = args.dateRange || "30d";
    const now = Date.now();
    const daysBack = dateRange === "7d" ? 7 : dateRange === "30d" ? 30 : dateRange === "90d" ? 90 : 365;
    const startDate = new Date(now - (daysBack * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];

    // Get page views
    const pageViews = await ctx.db
      .query("pageViews")
      .withIndex("by_date", (q) => q.gte("date", startDate))
      .collect();

    // Get content interactions
    const contentInteractions = await ctx.db
      .query("contentInteractions")
      .withIndex("by_date", (q) => q.gte("date", startDate))
      .collect();

    // Get form submissions
    const formSubmissions = await ctx.db
      .query("formSubmissions")
      .withIndex("by_date", (q) => q.gte("date", startDate))
      .collect();

    // Get all content for performance metrics
    const allContent = await ctx.db.query("content").collect();
    const allUsers = await ctx.db.query("users").collect();

    // Calculate metrics
    const totalPageViews = pageViews.length;
    const uniqueVisitors = new Set(pageViews.map(pv => pv.sessionId)).size;
    const totalFormSubmissions = formSubmissions.length;
    const successfulSubmissions = formSubmissions.filter(fs => fs.success).length;

    // Page views by day
    const pageViewsByDay = pageViews.reduce((acc, pv) => {
      acc[pv.date] = (acc[pv.date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Top pages
    const topPages = Object.entries(
      pageViews.reduce((acc, pv) => {
        acc[pv.path] = (acc[pv.path] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    )
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([path, views]) => ({ path, views }));

    // Content performance
    const contentPerformance = Object.entries(
      contentInteractions.reduce((acc, ci) => {
        if (!acc[ci.contentId]) {
          acc[ci.contentId] = { views: 0, edits: 0, publishes: 0 };
        }
        if (ci.action === "view") acc[ci.contentId].views++;
        if (ci.action === "edit") acc[ci.contentId].edits++;
        if (ci.action === "publish") acc[ci.contentId].publishes++;
        return acc;
      }, {} as Record<string, any>)
    )
      .sort(([, a], [, b]) => b.views - a.views)
      .slice(0, 10)
      .map(([contentId, metrics]) => ({ contentId, ...metrics }));

    // User activity
    const userActivity = allUsers.map(user => {
      const userInteractions = contentInteractions.filter(ci => ci.userId === user._id);
      return {
        userId: user._id,
        name: `${user.firstName} ${user.lastName}`,
        role: user.role,
        interactions: userInteractions.length,
        lastActivity: userInteractions.length > 0 
          ? Math.max(...userInteractions.map(ci => ci.timestamp))
          : user.createdAt,
      };
    }).sort((a, b) => b.interactions - a.interactions);

    return {
      overview: {
        totalPageViews,
        uniqueVisitors,
        totalFormSubmissions,
        successfulSubmissions,
        conversionRate: totalPageViews > 0 ? (successfulSubmissions / totalPageViews * 100) : 0,
        totalContent: allContent.length,
        publishedContent: allContent.filter(c => c.status === "published").length,
        totalUsers: allUsers.length,
        activeUsers: allUsers.filter(u => u.isActive).length,
      },
      charts: {
        pageViewsByDay,
        topPages,
        contentPerformance,
        userActivity: userActivity.slice(0, 10),
      },
      dateRange,
      lastUpdated: now,
    };
  },
});

// Get content performance analytics
export const getContentAnalytics = query({
  args: {
    contentId: v.optional(v.string()),
    dateRange: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Re-enable authentication once Clerk-Convex integration is fixed
    // const user = await getCurrentUser(ctx);
    // if (!hasAnalyticsPermission(user)) {
    //   throw new Error("Insufficient permissions to view analytics");
    // }

    const dateRange = args.dateRange || "30d";
    const now = Date.now();
    const daysBack = dateRange === "7d" ? 7 : dateRange === "30d" ? 30 : dateRange === "90d" ? 90 : 365;
    const startDate = new Date(now - (daysBack * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];

    let interactions = await ctx.db
      .query("contentInteractions")
      .withIndex("by_date", (q) => q.gte("date", startDate))
      .collect();

    if (args.contentId) {
      interactions = interactions.filter(ci => ci.contentId === args.contentId);
    }

    // Group by content and calculate metrics
    const contentMetrics = interactions.reduce((acc, interaction) => {
      if (!acc[interaction.contentId]) {
        acc[interaction.contentId] = {
          contentId: interaction.contentId,
          contentType: interaction.contentType,
          views: 0,
          edits: 0,
          publishes: 0,
          deletes: 0,
          lastActivity: 0,
        };
      }
      
      const metric = acc[interaction.contentId];
      if (interaction.action === "view") metric.views++;
      if (interaction.action === "edit") metric.edits++;
      if (interaction.action === "publish") metric.publishes++;
      if (interaction.action === "delete") metric.deletes++;
      
      metric.lastActivity = Math.max(metric.lastActivity, interaction.timestamp);
      
      return acc;
    }, {} as Record<string, any>);

    return {
      contentMetrics: Object.values(contentMetrics),
      totalInteractions: interactions.length,
      dateRange,
      lastUpdated: now,
    };
  },
});

// Get user activity analytics
export const getUserActivityAnalytics = query({
  args: {
    userId: v.optional(v.id("users")),
    dateRange: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Re-enable authentication once Clerk-Convex integration is fixed
    // const user = await getCurrentUser(ctx);
    // if (!hasAnalyticsPermission(user)) {
    //   throw new Error("Insufficient permissions to view analytics");
    // }

    const dateRange = args.dateRange || "30d";
    const now = Date.now();
    const daysBack = dateRange === "7d" ? 7 : dateRange === "30d" ? 30 : dateRange === "90d" ? 90 : 365;
    const startDate = new Date(now - (daysBack * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];

    let interactions = await ctx.db
      .query("contentInteractions")
      .withIndex("by_date", (q) => q.gte("date", startDate))
      .collect();

    if (args.userId) {
      interactions = interactions.filter(ci => ci.userId === args.userId);
    }

    // Activity by day
    const activityByDay = interactions.reduce((acc, interaction) => {
      acc[interaction.date] = (acc[interaction.date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Activity by action type
    const activityByAction = interactions.reduce((acc, interaction) => {
      acc[interaction.action] = (acc[interaction.action] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Most active users (if not filtering by specific user)
    let mostActiveUsers: any[] = [];
    if (!args.userId) {
      const userInteractions = interactions.reduce((acc, interaction) => {
        if (interaction.userId) {
          acc[interaction.userId] = (acc[interaction.userId] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      const users = await ctx.db.query("users").collect();
      mostActiveUsers = Object.entries(userInteractions)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([userId, count]) => {
          const userData = users.find(u => u._id === userId);
          return {
            userId,
            name: userData ? `${userData.firstName} ${userData.lastName}` : "Unknown",
            role: userData?.role || "unknown",
            interactions: count,
          };
        });
    }

    return {
      activityByDay,
      activityByAction,
      mostActiveUsers,
      totalInteractions: interactions.length,
      dateRange,
      lastUpdated: now,
    };
  },
});

// Get system performance metrics
export const getSystemMetrics = query({
  args: {},
  handler: async (ctx) => {
    // TODO: Re-enable authentication once Clerk-Convex integration is fixed
    // const user = await getCurrentUser(ctx);
    // if (!hasAnalyticsPermission(user)) {
    //   throw new Error("Insufficient permissions to view analytics");
    // }

    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);
    const last7Days = now - (7 * 24 * 60 * 60 * 1000);

    // Get recent data
    const recentPageViews = await ctx.db
      .query("pageViews")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", last24Hours))
      .collect();

    const recentInteractions = await ctx.db
      .query("contentInteractions")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", last24Hours))
      .collect();

    const recentFormSubmissions = await ctx.db
      .query("formSubmissions")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", last24Hours))
      .collect();

    // Calculate performance metrics
    const avgResponseTime = 150; // Simulated - in real app, track actual response times
    const errorRate = recentFormSubmissions.length > 0 
      ? ((recentFormSubmissions.filter(fs => !fs.success).length / recentFormSubmissions.length) * 100)
      : 0;

    return {
      performance: {
        avgResponseTime,
        errorRate,
        uptime: 99.9, // Simulated
        requestsPerSecond: recentPageViews.length / (24 * 60 * 60),
      },
      activity: {
        pageViewsLast24h: recentPageViews.length,
        interactionsLast24h: recentInteractions.length,
        submissionsLast24h: recentFormSubmissions.length,
      },
      lastUpdated: now,
    };
  },
});
