import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { ChevronDown } from 'lucide-react';
import * as Icons from "lucide-react";
import { useLanguage, useTranslation } from '@/contexts/I18nContext';

interface ServicesDropdownProps {
  isActive: boolean;
  className?: string;
}

export const ServicesDropdown = ({ isActive, className = "" }: ServicesDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Get all service cards in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for service cards and sort by order
  const serviceCards = allContent?.filter(item => 
    item.contentType?.name === "service_card" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  const getIconComponent = (iconName: string) => {
    if (!iconName) return Icons.Briefcase;
    const IconComponent = (Icons as any)[iconName];
    return IconComponent || Icons.Briefcase;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`font-medium transition-colors duration-200 flex items-center ${className} ${
          isActive
            ? 'text-blue-600 border-b-2 border-blue-600'
            : 'text-gray-700 hover:text-blue-600'
        }`}
      >
        {t('nav.services')}
        <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* All Services Link */}
          <Link
            to="/services"
            className="block px-4 py-3 text-gray-900 hover:bg-gray-50 border-b border-gray-100"
            onClick={() => setIsOpen(false)}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <Icons.Grid3X3 className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <div className="font-medium">{language === 'en' ? 'All Services' : 'Todos los Servicios'}</div>
                <div className="text-sm text-gray-500">{language === 'en' ? 'View our complete service portfolio' : 'Ver nuestro portafolio completo de servicios'}</div>
              </div>
            </div>
          </Link>

          {/* Individual Service Links */}
          {serviceCards.map((service) => {
            const IconComponent = getIconComponent(service.data.icon);
            return (
              <Link
                key={service._id}
                to={`/services/${service.data.slug}`}
                className="block px-4 py-3 text-gray-900 hover:bg-gray-50 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <IconComponent className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium">{service.data.title}</div>
                    <div 
                      className="text-sm text-gray-500 line-clamp-1"
                      dangerouslySetInnerHTML={{ 
                        __html: service.data.description.replace(/<[^>]*>/g, '').substring(0, 60) + '...' 
                      }}
                    />
                  </div>
                </div>
              </Link>
            );
          })}

          {serviceCards.length === 0 && (
            <div className="px-4 py-3 text-gray-500 text-sm">
              No services available
            </div>
          )}
        </div>
      )}
    </div>
  );
};
