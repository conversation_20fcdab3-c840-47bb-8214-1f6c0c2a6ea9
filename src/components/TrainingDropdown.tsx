import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, GraduationCap, Monitor, Target, Clock, Users, Award } from 'lucide-react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useLanguage, useTranslation } from '@/contexts/I18nContext';

interface TrainingDropdownProps {
  isActive: boolean;
  className?: string;
}

export const TrainingDropdown = ({ isActive, className = "" }: TrainingDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Get all training program cards in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for training program cards and sort by order
  const trainingPrograms = allContent?.filter(item =>
    item.contentType?.name === "training_program" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  // Fallback data if no dynamic content is available
  const fallbackTrainingPrograms = [
    {
      id: 'corporate-pc-skills',
      title: language === 'en' ? 'Corporate PC Skills' : 'Habilidades de PC Corporativas',
      description: language === 'en' ? 'Essential computer skills for the modern workplace' : 'Habilidades informáticas esenciales para el lugar de trabajo moderno',
      icon: Monitor,
      slug: 'corporate-pc-skills'
    },
    {
      id: 'leadership-training',
      title: language === 'en' ? 'Leadership Development' : 'Desarrollo de Liderazgo',
      description: language === 'en' ? 'Essential skills to lead effectively and inspire others' : 'Habilidades esenciales para liderar efectivamente e inspirar a otros',
      icon: Target,
      slug: 'leadership-training'
    },
    {
      id: 'time-management',
      title: language === 'en' ? 'Time Management' : 'Gestión del Tiempo',
      description: language === 'en' ? 'Take control of schedules and achieve more with less stress' : 'Toma control de horarios y logra más con menos estrés',
      icon: Clock,
      slug: 'time-management'
    },
    {
      id: 'communication-skills',
      title: language === 'en' ? 'Communication Skills' : 'Habilidades de Comunicación',
      description: language === 'en' ? 'Master the art of effective communication' : 'Domina el arte de la comunicación efectiva',
      icon: Users,
      slug: 'communication-skills'
    },
    {
      id: 'oil-gas-training',
      title: language === 'en' ? 'Oil & Gas Training' : 'Entrenamiento de Petróleo y Gas',
      description: language === 'en' ? 'Professional training for energy sector standards' : 'Entrenamiento profesional para estándares del sector energético',
      icon: Award,
      slug: 'oil-gas-training'
    }
  ];

  // Use dynamic content if available, otherwise fallback
  const displayPrograms = trainingPrograms.length > 0 ? trainingPrograms : fallbackTrainingPrograms;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`font-medium transition-colors duration-200 flex items-center ${className} ${
          isActive
            ? 'text-purple-600 border-b-2 border-purple-600'
            : 'text-gray-700 hover:text-purple-600'
        }`}
      >
        {language === 'en' ? 'Training' : 'Entrenamiento'}
        <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* All Training Link */}
          <Link
            to="/training"
            className="block px-4 py-3 text-gray-900 hover:bg-gray-50 border-b border-gray-100"
            onClick={() => setIsOpen(false)}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <GraduationCap className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <div className="font-medium">{language === 'en' ? 'All Training Programs' : 'Todos los Programas de Entrenamiento'}</div>
                <div className="text-sm text-gray-500">{language === 'en' ? 'View our complete training portfolio' : 'Ver nuestro portafolio completo de entrenamiento'}</div>
              </div>
            </div>
          </Link>

          {/* Individual Training Program Links */}
          {displayPrograms.map((program) => {
            // Handle both dynamic content and fallback data
            const IconComponent = program.icon || GraduationCap;
            const title = program.data?.title || program.title;
            const description = program.data?.description || program.description;
            const slug = program.data?.slug || program.slug;

            return (
              <Link
                key={program.id || program._id}
                to={`/training/${slug}`}
                className="block px-4 py-3 text-gray-900 hover:bg-gray-50 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                    <IconComponent className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <div className="font-medium">{title}</div>
                    <div className="text-sm text-gray-500 line-clamp-1">
                      {description}
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
};
