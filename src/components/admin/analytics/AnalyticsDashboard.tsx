import { useState } from "react";
import { useDashboardAnalytics, useSystemMetrics, formatAnalyticsData, analyticsDateRanges } from "@/hooks/useAnalytics";
import { useAuth } from "@/hooks/useAuth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { 
  BarChart3, 
  Users, 
  Eye, 
  FileText,
  TrendingUp,
  TrendingDown,
  Activity,
  Globe,
  Clock,
  Shield,
  Zap,
  RefreshCw
} from "lucide-react";
import { AnalyticsChart } from "./AnalyticsChart";
import { TopPagesTable } from "./TopPagesTable";
import { UserActivityTable } from "./UserActivityTable";
import { ContentPerformanceTable } from "./ContentPerformanceTable";

export const AnalyticsDashboard = () => {
  const { canViewAnalytics } = useAuth();
  const [dateRange, setDateRange] = useState("30d");
  const { analytics, isLoading } = useDashboardAnalytics(dateRange);
  const { metrics } = useSystemMetrics();

  if (!canViewAnalytics) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">You don't have permission to view analytics</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const overview = analytics?.overview;
  const charts = analytics?.charts;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <p className="text-gray-600">
            Monitor your website performance and user engagement
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {analyticsDateRanges.map(range => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Page Views"
            value={formatAnalyticsData.formatNumber(overview.totalPageViews)}
            icon={Eye}
            color="blue"
            trend={12.5}
          />
          <MetricCard
            title="Unique Visitors"
            value={formatAnalyticsData.formatNumber(overview.uniqueVisitors)}
            icon={Users}
            color="green"
            trend={8.2}
          />
          <MetricCard
            title="Form Submissions"
            value={formatAnalyticsData.formatNumber(overview.totalFormSubmissions)}
            icon={FileText}
            color="purple"
            trend={-2.1}
          />
          <MetricCard
            title="Conversion Rate"
            value={formatAnalyticsData.formatPercentage(overview.conversionRate)}
            icon={TrendingUp}
            color="orange"
            trend={5.7}
          />
        </div>
      )}

      {/* System Performance */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Avg Response Time</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.performance.avgResponseTime}ms</p>
                </div>
                <Zap className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Error Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{formatAnalyticsData.formatPercentage(metrics.performance.errorRate)}</p>
                </div>
                <Shield className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Uptime</p>
                  <p className="text-2xl font-bold text-gray-900">{formatAnalyticsData.formatPercentage(metrics.performance.uptime)}</p>
                </div>
                <Activity className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Requests/sec</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.performance.requestsPerSecond.toFixed(2)}</p>
                </div>
                <Globe className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts and Tables */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content Performance</TabsTrigger>
          <TabsTrigger value="users">User Activity</TabsTrigger>
          <TabsTrigger value="pages">Top Pages</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Page Views Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Page Views Over Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                {charts?.pageViewsByDay && (
                  <AnalyticsChart
                    data={formatAnalyticsData.generateTimeSeriesData(charts.pageViewsByDay, dateRange)}
                    type="line"
                    color="#3B82F6"
                  />
                )}
              </CardContent>
            </Card>

            {/* Content Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Content Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Content</span>
                  <span className="font-semibold">{overview?.totalContent}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Published</span>
                  <span className="font-semibold text-green-600">{overview?.publishedContent}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Users</span>
                  <span className="font-semibold">{overview?.totalUsers}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Active Users</span>
                  <span className="font-semibold text-blue-600">{overview?.activeUsers}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <ContentPerformanceTable data={charts?.contentPerformance || []} />
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <UserActivityTable data={charts?.userActivity || []} />
        </TabsContent>

        <TabsContent value="pages" className="space-y-4">
          <TopPagesTable data={charts?.topPages || []} />
        </TabsContent>
      </Tabs>

      {/* Last Updated */}
      <div className="text-center text-sm text-gray-500">
        <Clock className="w-4 h-4 inline mr-1" />
        Last updated: {analytics?.lastUpdated ? formatAnalyticsData.formatTimeAgo(analytics.lastUpdated) : 'Never'}
      </div>
    </div>
  );
};

// Metric Card Component
const MetricCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color, 
  trend 
}: {
  title: string;
  value: string;
  icon: any;
  color: string;
  trend?: number;
}) => {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-100",
    purple: "text-purple-600 bg-purple-100",
    orange: "text-orange-600 bg-orange-100",
  };

  const trendDirection = trend ? (trend > 0 ? 'up' : 'down') : 'neutral';
  const TrendIcon = trendDirection === 'up' ? TrendingUp : TrendingDown;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {trend !== undefined && (
              <div className={`flex items-center mt-1 text-sm ${
                trendDirection === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendIcon className="w-4 h-4 mr-1" />
                {Math.abs(trend).toFixed(1)}%
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full ${colorClasses[color as keyof typeof colorClasses]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
