import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useContentTypes } from "@/hooks/useContentTypes";
import { useContentMutations } from "@/hooks/useContent";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  ArrowLeft, 
  Save, 
  Eye, 
  Wand2,
  Info,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { FieldEditor } from "./FieldEditor";

export const ContentCreator = () => {
  const navigate = useNavigate();
  const { contentTypes, isLoading: isLoadingTypes } = useContentTypes();
  const { upsertContent } = useContentMutations();
  
  const [selectedTypeId, setSelectedTypeId] = useState<string>("");
  const [identifier, setIdentifier] = useState("");
  const [language, setLanguage] = useState("en");
  const [formData, setFormData] = useState<any>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  const selectedType = contentTypes?.find(type => type._id === selectedTypeId);

  // Initialize form data when content type changes
  useEffect(() => {
    if (selectedType) {
      const initialData: any = {};
      selectedType.fields.forEach(field => {
        if (field.defaultValue !== undefined) {
          initialData[field.name] = field.defaultValue;
        } else {
          // Set appropriate default values based on field type
          switch (field.type) {
            case "text":
            case "textarea":
            case "richtext":
              initialData[field.name] = "";
              break;
            case "number":
              initialData[field.name] = 0;
              break;
            case "boolean":
              initialData[field.name] = false;
              break;
            case "array":
              initialData[field.name] = [];
              break;
            case "object":
              initialData[field.name] = {};
              break;
            default:
              initialData[field.name] = "";
          }
        }
      });
      setFormData(initialData);
    }
  }, [selectedType]);

  // Generate identifier from title/name field
  useEffect(() => {
    if (formData.title || formData.name || formData.heading) {
      const text = formData.title || formData.name || formData.heading;
      const slug = text
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
      if (slug && !identifier) {
        setIdentifier(slug);
      }
    }
  }, [formData.title, formData.name, formData.heading, identifier]);

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));
    // Clear error when field is modified
    if (errors[fieldName]) {
      setErrors(prev => ({ ...prev, [fieldName]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!identifier.trim()) {
      newErrors.identifier = "Identifier is required";
    }

    if (!selectedType) {
      newErrors.contentType = "Content type is required";
    }

    // Validate required fields
    selectedType?.fields.forEach(field => {
      if (field.required) {
        const value = formData[field.name];
        if (value === undefined || value === null || value === "") {
          newErrors[field.name] = `${field.label} is required`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (status: "draft" | "published" = "draft") => {
    if (!validateForm()) {
      toast.error("Please fix the errors before saving");
      return;
    }

    setIsSaving(true);
    try {
      await upsertContent({
        identifier,
        language,
        data: formData,
        contentTypeId: selectedTypeId as any,
        status,
      });

      toast.success(`Content ${status === "published" ? "published" : "saved as draft"} successfully`);
      navigate("/admin/content");
    } catch (error) {
      toast.error("Failed to save content");
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoadingTypes) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate("/admin/content")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Content
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Create Content</h2>
            <p className="text-gray-600">Add new content to your website</p>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            onClick={() => handleSave("draft")}
            disabled={isSaving || !selectedType}
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? "Saving..." : "Save Draft"}
          </Button>
          <Button 
            onClick={() => handleSave("published")}
            disabled={isSaving || !selectedType}
          >
            <Eye className="w-4 h-4 mr-2" />
            {isSaving ? "Publishing..." : "Publish"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Content Type Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="w-5 h-5" />
                Content Type
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="content-type">Select Content Type</Label>
                <Select value={selectedTypeId} onValueChange={setSelectedTypeId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a content type..." />
                  </SelectTrigger>
                  <SelectContent>
                    {contentTypes?.map(type => (
                      <SelectItem key={type._id} value={type._id}>
                        <div className="flex items-center space-x-2">
                          <span>{type.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {type.fields.length} fields
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.contentType && (
                  <p className="text-sm text-red-600 mt-1">{errors.contentType}</p>
                )}
              </div>

              {selectedType && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start gap-2">
                    <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">{selectedType.name}</p>
                      {selectedType.description && (
                        <p className="mt-1">{selectedType.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Content Fields */}
          {selectedType && (
            <Card>
              <CardHeader>
                <CardTitle>Content Fields</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {selectedType.fields.map((field) => (
                  <FieldEditor
                    key={field.name}
                    field={field}
                    value={formData[field.name]}
                    onChange={(value) => handleFieldChange(field.name, value)}
                    error={errors[field.name]}
                    disabled={isSaving}
                  />
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Content Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Content Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="identifier">Identifier</Label>
                <Input
                  id="identifier"
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                  placeholder="content-identifier"
                  disabled={isSaving}
                />
                {errors.identifier && (
                  <p className="text-sm text-red-600 mt-1">{errors.identifier}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Unique identifier for this content
                </p>
              </div>

              <div>
                <Label htmlFor="language">Language</Label>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Content Type Info */}
          {selectedType && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Content Type Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-900">{selectedType.name}</p>
                  {selectedType.description && (
                    <p className="text-xs text-gray-600 mt-1">{selectedType.description}</p>
                  )}
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Fields:</span>
                  <Badge variant="outline">{selectedType.fields.length}</Badge>
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Required:</span>
                  <Badge variant="outline">
                    {selectedType.fields.filter(f => f.required).length}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Validation Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Validation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <ValidationItem
                  label="Content Type"
                  isValid={!!selectedType}
                />
                <ValidationItem
                  label="Identifier"
                  isValid={!!identifier.trim()}
                />
                <ValidationItem
                  label="Required Fields"
                  isValid={selectedType ? selectedType.fields.filter(f => f.required).every(f => {
                    const value = formData[f.name];
                    return value !== undefined && value !== null && value !== "";
                  }) : false}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Validation Item Component
const ValidationItem = ({ label, isValid }: { label: string; isValid: boolean }) => (
  <div className="flex items-center justify-between text-sm">
    <span className="text-gray-600">{label}</span>
    {isValid ? (
      <CheckCircle className="w-4 h-4 text-green-600" />
    ) : (
      <AlertCircle className="w-4 h-4 text-red-600" />
    )}
  </div>
);
